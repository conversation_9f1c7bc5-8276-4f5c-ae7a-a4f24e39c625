/*
 Navicat Premium Dump SQL

 Source Server         : hei
 Source Server Type    : MySQL
 Source Server Version : 80406 (8.4.6)
 Source Host           : **************:3306
 Source Schema         : yuanshen

 Target Server Type    : MySQL
 Target Server Version : 80406 (8.4.6)
 File Encoding         : 65001

 Date: 29/08/2025 07:44:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for address
-- ----------------------------
DROP TABLE IF EXISTS `address`;
CREATE TABLE `address`  (
                            `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
                            `trxaddr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'TRX地址',
                            `ethaddr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ETH地址',
                            `trx` decimal(20, 5) NULL DEFAULT NULL COMMENT 'TRX余额',
                            `usdt_trc` decimal(20, 5) NULL DEFAULT NULL COMMENT '波场链USDT余额',
                            `usdt_erc` decimal(20, 5) NULL DEFAULT NULL COMMENT '以太坊链USDT余额',
                            `usdt_bsc` decimal(20, 5) NULL DEFAULT NULL COMMENT '币安链USDT余额',
                            `eth` decimal(20, 5) NULL DEFAULT NULL COMMENT 'ETH余额',
                            `bnb` decimal(20, 5) NULL DEFAULT NULL COMMENT 'BNB余额',
                            `num` smallint NULL DEFAULT NULL COMMENT '笔数',
                            `ercnum` smallint NULL DEFAULT NULL COMMENT 'erc笔数',
                            `bscnum` smallint NULL DEFAULT NULL COMMENT 'bsc笔数',
                            `lock` smallint NULL DEFAULT NULL,
                            `leaseid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                            `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '私钥',
                            `locktime` bigint NULL DEFAULT NULL COMMENT '锁定时间',
                            `userid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户绑定用户ID',
                            `callbackurl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调地址',
                            `merchant_id` smallint NULL DEFAULT NULL COMMENT '商户id',
                            `merchant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户名',
                            `del_flg` int NULL DEFAULT NULL,
                            `trxlock` smallint NULL DEFAULT NULL COMMENT '用于trc归集 记录状态',
                            `ethlock` smallint NULL DEFAULT NULL COMMENT '用于erc归集 记录状态',
                            `bnblock` smallint NULL DEFAULT NULL COMMENT '用于bsc归集 记录状态',
                            `ethhash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                            `usdthash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                            `trx_status` int NULL DEFAULT NULL,
                            `eth_status` int NOT NULL DEFAULT 1,
                            `bsc_status` int NULL DEFAULT 1,
                            `update_money` int NULL DEFAULT NULL,
                            `trcsid` int NOT NULL,
                            `ercsid` int NOT NULL,
                            `bscsid` int NOT NULL,
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of address
-- ----------------------------
INSERT INTO `address` VALUES (3, 'TBvgiaPt7SXuQgNAv5xJJGL6Wz933bcLjT', '******************************************', 0.00001, 0.00000, 0.00000, NULL, 0.00000, NULL, 1, 1, NULL, 0, '1', 'b3f37b68ac64d8e59e45f33fd431757c9e6b543026f6dd677b92768b14251fca49a105166c7eab1ded6ea76151d4a508aec6ddf5a35c508d480663f24ee8e5708edbd98c2670270f9c805cbbc43e9b2a866b4dd5e68e845a0075ad2919916fdc2e7e585f9e8b7449', NULL, '2', 'http://**************:8999/callback', 1, 'xn3469010', 0, 0, 0, NULL, NULL, NULL, 1, 1, 1, 0, 0, 0, 0);
INSERT INTO `address` VALUES (4, 'TUqEB8xYACmrMWLojCUk9PLtadcGNa165g', '0xCEE80aC196E3e5F46dFf2e24487C77C3ACD722b5', 0.00000, 0.00000, 0.00000, NULL, 0.00000, NULL, 1, 1, NULL, 0, '1', '38b71d8a0a99eecad047aaa331b062da29b52cbcebf085ba92199d89caa1dce7b8d55be91aec3756c887073a709465ea559e60c6b2099d60f475bbe95bba733014a9fb5b3fc50c1d01fbe825375af35336a91571f8c8bca5dce41dc0bd244fac1c2a50d1a8b720d0', NULL, '1', 'http://**************:8999/callback', 1, 'xn3469010', 0, 0, 0, NULL, NULL, NULL, 1, 1, 1, 0, 0, 0, 0);
INSERT INTO `address` VALUES (5, 'TEw7Lir1CJp3BqCJ7X6w9x8DpCbtQgramK', '0x3673124a79fBBF1F1b39F22CFF9b924097beAC4f', 4.38600, 5.00000, 0.00000, 0.00000, 0.00000, 0.00000, 1, 1, 0, 0, '1', 'a9e94ab46d338c7600eddd6be39a83df77ef80c8cccd1684cba3471fa07caf20454628ed1e8556d78d28fa8b960997499143cd4dab117ffbb598f8f0f6683f0a0e5a37c4eafc7d79932663c9cf9cf907e4e2be01dd94458c144c78744df350c48c037d7771dccde9', NULL, NULL, NULL, 1, 'xn3469010', 0, 0, 0, 0, NULL, NULL, 1, 1, 1, 0, 0, 0, 0);
INSERT INTO `address` VALUES (6, 'TNMtab5kw6BSGoR4AtgzFCj89VC1FTz8Te', '0x87Ebb2Dd9524FD4C75d47FDf09EFcc0ecD639954', 0.00000, 0.10000, 0.00000, NULL, 0.00000, NULL, 1, 1, NULL, 0, '1', '4fcb07dfe1775563fc4b411324f0a38b24cae93e6cb0c442264c10923267974b7d79b9710c23f2199b79318dbb2457ddf072aaf4fc03649e6cfe94321b142616ce37322b4d3549cf6541559091c234846e964ffa5ae09ae70d61a58ad59f3931b0e955b7ef016723', NULL, NULL, NULL, 888, 'shanghu', 0, 0, 0, NULL, NULL, NULL, 1, 1, 1, 0, 0, 0, 0);
INSERT INTO `address` VALUES (7, 'TAvjuzxjj61xiWTLkZk6yrxHMw2ksRxtad', '0x0a80Aebdf92e906Ab1c1d39a7c965bD707F8280f', 4.00000, 2.00000, 0.00000, 0.00000, 0.00000, 0.00000, 1, 1, 0, 0, '1', '727de306bbb6236d3a4786f49bb2da0b001fa44f2ea253549957b36a5222a2f348edae4a120c50f457b0d209b29ff3a722cd9cc9d152d2b1ab08be618077fc39de89077b15a24e9915e5762982390568d8baf0568209ccf31e793b0cba978418cad449efaf26d170', NULL, NULL, NULL, 1, 'xn3469010', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 0, 0);
INSERT INTO `address` VALUES (8, 'TZJSAKsQQFo6hCd37eTu1Mmu491y1XR2Yy', '0xfFeDC8126485af3aE33b5C77De8C7567bD390981', 0.00000, 0.00000, 0.00000, NULL, 0.00000, NULL, 1, 1, NULL, 0, NULL, '5c6a13d495b923606d7357add0cf36471c597a5558f5e77361cd13becc4ead283aec9b6711e0d260e5a8841504333c4c7e93eaa8801418c8dabf50cb8e83f52eb9e2b7261dec4f32190ce0f9e621d8c3416a30ecbf9efd8f300a5109a88b4111a49c0b9560beeb7e', NULL, NULL, NULL, 888, 'shanghu', 0, 0, 0, NULL, NULL, NULL, 0, 1, 1, 0, 0, 0, 0);
INSERT INTO `address` VALUES (9, 'TCYKXWvErR6H4KhQWSG7PM91918qQ3fiXx', '0x1c336CDAB6BbFe675Ce34Fa5fD51DA751eBe8Ccf', 0.00000, 0.00000, 0.00000, NULL, 0.00000, NULL, 1, 1, NULL, 0, NULL, 'bd352500b55ea758f0af1099faa7a82d9cbfcc66a2c562f89b15d77c5258b112a47e40aa7614ac226e0045eff3528c89b24b4c2eb8a2f1ba72cab9b92678fd8e56b987d6643317358c16f0313910d62d3946a85fec216836f0881ea39e48ce61db407641300db397', NULL, NULL, NULL, 888, 'shanghu', 0, 0, 0, NULL, NULL, NULL, 0, 1, 1, 0, 0, 0, 0);
INSERT INTO `address` VALUES (10, 'TJXVHWSLEhNpub39UWENysRf1ZSE63aQuL', '0x5ddBE8f086260a70d36B04F16D4d277d3f42E4e0', 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0, 0, 0, 0, '1', '938f4dfc4f32150c2633512f8ab4dbba2113a98ac9d6a19542a650a474dc625fabfdea56dc246f04d13dbb3b3543d4f7555bf7ab44ac072a4813303d93f187497ff028ebb009818624078814b03683c00b56d2f10454cc62cac6bd5801419634754957c296bfd4e2', NULL, NULL, NULL, 1, 'xn3469010', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 0, 0);
INSERT INTO `address` VALUES (11, 'TBdcu2hnsmpshcq1srQ6cwwcuoiydvqTdR', '0x123be1378C7f5a4d351D46e508B634D3dD37805B', 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0.00000, 0, 0, 0, 0, '1', 'aa1e7a4c2547f55bd563e27b9220629648a21fe8793535d00ed1cf7867c7c3847c49deb3dd15046a51ac87001840ec37ba2e03164d5034da2e95aaf80b9423f94db3f954adc1c988f0ec47c04d3fb8776613aab822b38ea96e369c52666f61790862334860e2f0b5', NULL, NULL, NULL, 1, 'xn3469010', 0, 0, 0, 0, NULL, NULL, 0, 1, 1, 0, 0, 0, 0);

SET FOREIGN_KEY_CHECKS = 1;
