-- =====================================================
-- 数字货币钱包地址管理系统 - PostgreSQL 优化版
-- 修复索引名冲突问题
-- =====================================================

-- 设置客户端编码
SET client_encoding = 'UTF8';

-- =====================================================
-- 1. 链类型字典表
-- =====================================================
DROP TABLE IF EXISTS chain_type_dict CASCADE;
CREATE TABLE chain_type_dict (
    id SMALLSERIAL PRIMARY KEY,
    chain_name VARCHAR(50) NOT NULL,
    chain_code VARCHAR(20) NOT NULL,
    address_prefix VARCHAR(50),
    native_asset VARCHAR(20) NOT NULL,
    decimals SMALLINT NOT NULL DEFAULT 18,
    is_evm BOOLEAN NOT NULL DEFAULT FALSE,
    status SMALLINT NOT NULL DEFAULT 1,
    remark VARCHAR(255),
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引和约束
CREATE UNIQUE INDEX uk_chain_type_code ON chain_type_dict(chain_code);
CREATE INDEX idx_chain_type_status ON chain_type_dict(status);

-- 添加注释
COMMENT ON TABLE chain_type_dict IS '链类型字典表';
COMMENT ON COLUMN chain_type_dict.id IS '链类型唯一ID，自增主键';
COMMENT ON COLUMN chain_type_dict.chain_name IS '链名称（中文），如：波场链（TRON）';
COMMENT ON COLUMN chain_type_dict.chain_code IS '链编码（如TRX/ETH/BSC），用于代码枚举';
COMMENT ON COLUMN chain_type_dict.address_prefix IS '地址前缀（多个用逗号分隔），用于格式校验';
COMMENT ON COLUMN chain_type_dict.native_asset IS '原生币符号，如TRX/ETH/BNB';
COMMENT ON COLUMN chain_type_dict.decimals IS '原生币精度（小数位数），用于资产计算';
COMMENT ON COLUMN chain_type_dict.is_evm IS '是否EVM兼容链（true=是，false=否）';
COMMENT ON COLUMN chain_type_dict.status IS '状态（1=启用 0=禁用），控制链是否可用';
COMMENT ON COLUMN chain_type_dict.remark IS '备注信息，补充链特性说明';
COMMENT ON COLUMN chain_type_dict.created_at IS '记录创建时间';
COMMENT ON COLUMN chain_type_dict.updated_at IS '记录最后更新时间';

-- =====================================================
-- 2. 代币表
-- =====================================================
DROP TABLE IF EXISTS token CASCADE;
CREATE TABLE token (
    token_id VARCHAR(80) PRIMARY KEY,
    chain_id SMALLINT NOT NULL,
    token_name VARCHAR(100) NOT NULL,
    token_symbol VARCHAR(20) NOT NULL,
    token_contract VARCHAR(255),
    token_decimals SMALLINT NOT NULL DEFAULT 18,
    token_status SMALLINT NOT NULL DEFAULT 1,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引和约束
CREATE UNIQUE INDEX uk_token_chain_contract ON token(chain_id, token_symbol, COALESCE(token_contract, ''));
CREATE INDEX idx_token_chain_symbol ON token(chain_id, token_symbol);
CREATE INDEX idx_token_status ON token(token_status);

-- 外键约束
ALTER TABLE token ADD CONSTRAINT fk_token_chain 
    FOREIGN KEY (chain_id) REFERENCES chain_type_dict(id) ON DELETE RESTRICT ON UPDATE CASCADE;

-- 添加注释
COMMENT ON TABLE token IS '代币表，管理所有支持的代币信息';
COMMENT ON COLUMN token.token_id IS '代币唯一标识，规则：chain_code_token_symbol_contract_short';
COMMENT ON COLUMN token.chain_id IS '所属链ID，关联chain_type_dict.id';
COMMENT ON COLUMN token.token_name IS '代币名称（含代币标准），如：Tether USD（TRC20）';
COMMENT ON COLUMN token.token_symbol IS '代币符号，如USDT/BNB/BTC';
COMMENT ON COLUMN token.token_contract IS '代币合约地址，原生币填NULL，ERC20/TRC20等填完整合约地址';
COMMENT ON COLUMN token.token_decimals IS '代币精度（小数位数），与链原生币可能不同';
COMMENT ON COLUMN token.token_status IS '代币状态（1=启用 0=禁用），控制代币是否可用';
COMMENT ON COLUMN token.created_at IS '记录创建时间';
COMMENT ON COLUMN token.updated_at IS '记录最后更新时间';

-- =====================================================
-- 3. 商户表
-- =====================================================
DROP TABLE IF EXISTS merchant CASCADE;
CREATE TABLE merchant (
    id SERIAL PRIMARY KEY,
    merchant_name VARCHAR(100) NOT NULL,
    callback_url VARCHAR(500),
    status SMALLINT NOT NULL DEFAULT 1,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE UNIQUE INDEX uk_merchant_name ON merchant(merchant_name);
CREATE INDEX idx_merchant_status ON merchant(status);

-- 添加注释
COMMENT ON TABLE merchant IS '商户表，管理系统中的商户信息';
COMMENT ON COLUMN merchant.id IS '商户ID，自增主键';
COMMENT ON COLUMN merchant.merchant_name IS '商户名称，唯一标识商户';
COMMENT ON COLUMN merchant.callback_url IS '默认回调地址，商户接收通知的URL';
COMMENT ON COLUMN merchant.status IS '商户状态（1=启用 0=禁用）';
COMMENT ON COLUMN merchant.created_at IS '商户创建时间';
COMMENT ON COLUMN merchant.updated_at IS '商户信息最后更新时间';

-- =====================================================
-- 4. 私钥表（独立存储，严格权限控制）
-- =====================================================
DROP TABLE IF EXISTS address_private_key CASCADE;
CREATE TABLE address_private_key (
    id SERIAL PRIMARY KEY,
    key_hash VARCHAR(64) NOT NULL,
    encrypted_private_key BYTEA NOT NULL,
    key_fingerprint BYTEA,
    encryption_method VARCHAR(20) NOT NULL DEFAULT 'AES256',
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE UNIQUE INDEX uk_private_key_hash ON address_private_key(key_hash);

-- 添加注释
COMMENT ON TABLE address_private_key IS '私钥表（严格权限控制），独立存储加密私钥';
COMMENT ON COLUMN address_private_key.id IS '私钥记录ID，自增主键';
COMMENT ON COLUMN address_private_key.key_hash IS '私钥哈希（用于去重和索引），SHA256哈希值';
COMMENT ON COLUMN address_private_key.encrypted_private_key IS '加密后的私钥，二进制存储';
COMMENT ON COLUMN address_private_key.key_fingerprint IS '密钥指纹，用于验证密钥完整性';
COMMENT ON COLUMN address_private_key.encryption_method IS '加密方法，如AES256/ChaCha20';
COMMENT ON COLUMN address_private_key.created_at IS '私钥创建时间';

-- =====================================================
-- 5. 地址主表（地址组）
-- =====================================================
DROP TABLE IF EXISTS address_main CASCADE;
CREATE TABLE address_main (
    id SERIAL PRIMARY KEY,
    merchant_id INTEGER NOT NULL,
    user_id VARCHAR(64),
    group_name VARCHAR(50),
    locked_until TIMESTAMP(3) WITH TIME ZONE,
    deleted_at TIMESTAMP(3) WITH TIME ZONE,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引和约束（修复索引名冲突）
CREATE INDEX idx_address_main_merchant_user ON address_main(merchant_id, user_id);
CREATE INDEX idx_address_main_deleted_at ON address_main(deleted_at);
CREATE INDEX idx_address_main_locked_until ON address_main(locked_until);

-- 外键约束
ALTER TABLE address_main ADD CONSTRAINT fk_address_main_merchant 
    FOREIGN KEY (merchant_id) REFERENCES merchant(id) ON DELETE RESTRICT ON UPDATE CASCADE;

-- 添加注释
COMMENT ON TABLE address_main IS '地址主表（地址组），管理商户的地址组';
COMMENT ON COLUMN address_main.id IS '地址组ID，自增主键';
COMMENT ON COLUMN address_main.merchant_id IS '商户ID，关联merchant.id';
COMMENT ON COLUMN address_main.user_id IS '用户ID，标识地址组所属商户下的具体用户';
COMMENT ON COLUMN address_main.group_name IS '地址组名称，如"日常收款组"，便于管理';
COMMENT ON COLUMN address_main.locked_until IS '锁定到期时间，NULL表示未锁定';
COMMENT ON COLUMN address_main.deleted_at IS '删除时间（软删除），NULL表示未删除';
COMMENT ON COLUMN address_main.created_at IS '地址组创建时间';
COMMENT ON COLUMN address_main.updated_at IS '地址组最后更新时间';

-- =====================================================
-- 6. 链地址子表
-- =====================================================
DROP TABLE IF EXISTS address_chain CASCADE;
CREATE TABLE address_chain (
    id SERIAL PRIMARY KEY,
    address_main_id INTEGER NOT NULL,
    chain_id SMALLINT NOT NULL,
    chain_addr VARCHAR(255) NOT NULL,
    private_key_id INTEGER,
    callback_url VARCHAR(500),
    lock_status SMALLINT NOT NULL DEFAULT 0,
    sync_status SMALLINT NOT NULL DEFAULT 1,
    last_sync_block BIGINT,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引和约束（修复索引名冲突）
CREATE UNIQUE INDEX uk_address_chain_main_chain ON address_chain(address_main_id, chain_id);
CREATE UNIQUE INDEX uk_address_chain_addr ON address_chain(chain_id, chain_addr);
CREATE INDEX idx_address_chain_sync_status ON address_chain(sync_status);
CREATE INDEX idx_address_chain_lock_status ON address_chain(lock_status);
CREATE INDEX idx_address_chain_sync_block ON address_chain(last_sync_block);

-- 外键约束
ALTER TABLE address_chain ADD CONSTRAINT fk_address_chain_main
    FOREIGN KEY (address_main_id) REFERENCES address_main(id) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE address_chain ADD CONSTRAINT fk_address_chain_type
    FOREIGN KEY (chain_id) REFERENCES chain_type_dict(id) ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE address_chain ADD CONSTRAINT fk_address_chain_key
    FOREIGN KEY (private_key_id) REFERENCES address_private_key(id) ON DELETE SET NULL ON UPDATE CASCADE;

-- 添加注释
COMMENT ON TABLE address_chain IS '链地址子表，存储具体链地址信息';
COMMENT ON COLUMN address_chain.id IS '地址记录ID，自增主键';
COMMENT ON COLUMN address_chain.address_main_id IS '地址组ID，关联address_main.id';
COMMENT ON COLUMN address_chain.chain_id IS '链ID，关联chain_type_dict.id';
COMMENT ON COLUMN address_chain.chain_addr IS '链地址，完整地址如TRX的T开头、ETH的0x开头';
COMMENT ON COLUMN address_chain.private_key_id IS '私钥ID，关联address_private_key.id，可选';
COMMENT ON COLUMN address_chain.callback_url IS '地址级回调URL，支持每个地址独立回调';
COMMENT ON COLUMN address_chain.lock_status IS '地址锁定状态（0=未锁定 1=已锁定）';
COMMENT ON COLUMN address_chain.sync_status IS '地址同步状态（1=正常 2=异常）';
COMMENT ON COLUMN address_chain.last_sync_block IS '最后同步的区块号';
COMMENT ON COLUMN address_chain.created_at IS '地址创建时间';
COMMENT ON COLUMN address_chain.updated_at IS '地址最后更新时间';

-- =====================================================
-- 7. 资产表
-- =====================================================
DROP TABLE IF EXISTS address_asset CASCADE;
CREATE TABLE address_asset (
    id SERIAL PRIMARY KEY,
    address_chain_id INTEGER NOT NULL,
    token_id VARCHAR(80) NOT NULL,
    balance_wei NUMERIC(38,0) NOT NULL DEFAULT 0,
    balance_decimal NUMERIC(36,18) NOT NULL DEFAULT 0,
    last_sync_time TIMESTAMP(3) WITH TIME ZONE,
    last_tx_hash VARCHAR(66),
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引和约束（修复索引名冲突）
CREATE UNIQUE INDEX uk_address_asset_addr_token ON address_asset(address_chain_id, token_id);
CREATE INDEX idx_address_asset_sync_time ON address_asset(last_sync_time);
CREATE INDEX idx_address_asset_balance ON address_asset(balance_decimal);

-- 外键约束
ALTER TABLE address_asset ADD CONSTRAINT fk_address_asset_chain
    FOREIGN KEY (address_chain_id) REFERENCES address_chain(id) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE address_asset ADD CONSTRAINT fk_address_asset_token
    FOREIGN KEY (token_id) REFERENCES token(token_id) ON DELETE RESTRICT ON UPDATE CASCADE;

-- 添加注释
COMMENT ON TABLE address_asset IS '资产表，存储地址的具体资产余额';
COMMENT ON COLUMN address_asset.id IS '资产记录ID，自增主键';
COMMENT ON COLUMN address_asset.address_chain_id IS '地址ID，关联address_chain.id';
COMMENT ON COLUMN address_asset.token_id IS '代币ID，关联token.token_id';
COMMENT ON COLUMN address_asset.balance_wei IS '余额（最小单位），如wei/sun，精确计算用';
COMMENT ON COLUMN address_asset.balance_decimal IS '余额（小数形式），展示用，保留18位小数';
COMMENT ON COLUMN address_asset.last_sync_time IS '最后同步时间，从链上同步余额的时间';
COMMENT ON COLUMN address_asset.last_tx_hash IS '最后交易哈希，用于增量同步';
COMMENT ON COLUMN address_asset.created_at IS '资产记录创建时间';
COMMENT ON COLUMN address_asset.updated_at IS '资产余额最后更新时间';

-- =====================================================
-- 8. 同步任务状态表
-- =====================================================
DROP TYPE IF EXISTS task_type_enum CASCADE;
CREATE TYPE task_type_enum AS ENUM ('sync', 'collect', 'withdraw', 'transfer');

DROP TABLE IF EXISTS address_sync_task CASCADE;
CREATE TABLE address_sync_task (
    id SERIAL PRIMARY KEY,
    address_chain_id INTEGER NOT NULL,
    task_type task_type_enum NOT NULL,
    task_status SMALLINT NOT NULL DEFAULT 0,
    lease_id VARCHAR(64),
    locked_until TIMESTAMP(3) WITH TIME ZONE,
    retry_count SMALLINT NOT NULL DEFAULT 0,
    max_retry SMALLINT NOT NULL DEFAULT 3,
    last_error TEXT,
    task_data JSONB,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP(3) WITH TIME ZONE
);

-- 创建索引和约束（修复索引名冲突）
CREATE UNIQUE INDEX uk_sync_task_addr_type ON address_sync_task(address_chain_id, task_type);
CREATE INDEX idx_sync_task_status ON address_sync_task(task_status);
CREATE INDEX idx_sync_task_lease_id ON address_sync_task(lease_id);
CREATE INDEX idx_sync_task_locked_until ON address_sync_task(locked_until);
CREATE INDEX idx_sync_task_retry_count ON address_sync_task(retry_count);
CREATE INDEX idx_sync_task_data_gin ON address_sync_task USING GIN(task_data);

-- 外键约束
ALTER TABLE address_sync_task ADD CONSTRAINT fk_sync_task_addr
    FOREIGN KEY (address_chain_id) REFERENCES address_chain(id) ON DELETE CASCADE ON UPDATE CASCADE;

-- 添加注释
COMMENT ON TABLE address_sync_task IS '同步任务状态表，管理地址同步、归集等任务';
COMMENT ON COLUMN address_sync_task.id IS '任务ID，自增主键';
COMMENT ON COLUMN address_sync_task.address_chain_id IS '地址ID，关联address_chain.id';
COMMENT ON COLUMN address_sync_task.task_type IS '任务类型，sync=同步 collect=归集 withdraw=提现 transfer=转账';
COMMENT ON COLUMN address_sync_task.task_status IS '任务状态（0=待处理 1=处理中 2=成功 3=失败）';
COMMENT ON COLUMN address_sync_task.lease_id IS '租约ID（并发控制），防止重复处理';
COMMENT ON COLUMN address_sync_task.locked_until IS '锁定到期时间，任务锁定机制';
COMMENT ON COLUMN address_sync_task.retry_count IS '当前重试次数';
COMMENT ON COLUMN address_sync_task.max_retry IS '最大重试次数，超过后标记失败';
COMMENT ON COLUMN address_sync_task.last_error IS '最后错误信息，失败时记录详细错误';
COMMENT ON COLUMN address_sync_task.task_data IS '任务数据（JSON格式），存储任务相关参数';
COMMENT ON COLUMN address_sync_task.created_at IS '任务创建时间';
COMMENT ON COLUMN address_sync_task.updated_at IS '任务最后更新时间';
COMMENT ON COLUMN address_sync_task.completed_at IS '任务完成时间，成功或失败时设置';

-- =====================================================
-- 9. 交易记录表
-- =====================================================
DROP TYPE IF EXISTS tx_type_enum CASCADE;
CREATE TYPE tx_type_enum AS ENUM ('in', 'out');

DROP TABLE IF EXISTS address_transaction CASCADE;
CREATE TABLE address_transaction (
    id BIGSERIAL PRIMARY KEY,
    address_chain_id INTEGER NOT NULL,
    token_id VARCHAR(80) NOT NULL,
    tx_hash VARCHAR(66) NOT NULL,
    tx_type tx_type_enum NOT NULL,
    amount_wei NUMERIC(38,0) NOT NULL DEFAULT 0,
    amount_decimal NUMERIC(36,18) NOT NULL DEFAULT 0,
    from_addr VARCHAR(255),
    to_addr VARCHAR(255),
    block_number BIGINT,
    block_time TIMESTAMP(3) WITH TIME ZONE,
    tx_status SMALLINT NOT NULL DEFAULT 0,
    confirmations INTEGER NOT NULL DEFAULT 0,
    gas_used BIGINT,
    gas_price BIGINT,
    created_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP(3) WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引和约束（修复索引名冲突）
CREATE UNIQUE INDEX uk_transaction_addr_hash ON address_transaction(address_chain_id, tx_hash);
CREATE INDEX idx_transaction_hash ON address_transaction(tx_hash);
CREATE INDEX idx_transaction_block_number ON address_transaction(block_number);
CREATE INDEX idx_transaction_block_time ON address_transaction(block_time);
CREATE INDEX idx_transaction_status ON address_transaction(tx_status);
CREATE INDEX idx_transaction_type ON address_transaction(tx_type);

-- 外键约束
ALTER TABLE address_transaction ADD CONSTRAINT fk_transaction_addr
    FOREIGN KEY (address_chain_id) REFERENCES address_chain(id) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE address_transaction ADD CONSTRAINT fk_transaction_token
    FOREIGN KEY (token_id) REFERENCES token(token_id) ON DELETE RESTRICT ON UPDATE CASCADE;

-- 添加注释
COMMENT ON TABLE address_transaction IS '交易记录表';
COMMENT ON COLUMN address_transaction.tx_type IS '交易类型（in=入账 out=出账）';
COMMENT ON COLUMN address_transaction.tx_status IS '交易状态（0=待确认 1=已确认 2=失败）';

-- =====================================================
-- 创建更新时间触发器函数
-- =====================================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间触发器
CREATE TRIGGER update_chain_type_dict_updated_at BEFORE UPDATE ON chain_type_dict
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_token_updated_at BEFORE UPDATE ON token
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_merchant_updated_at BEFORE UPDATE ON merchant
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_address_main_updated_at BEFORE UPDATE ON address_main
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_address_chain_updated_at BEFORE UPDATE ON address_chain
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_address_asset_updated_at BEFORE UPDATE ON address_asset
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_address_sync_task_updated_at BEFORE UPDATE ON address_sync_task
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_address_transaction_updated_at BEFORE UPDATE ON address_transaction
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- 初始化基础数据
-- =====================================================

-- 插入链类型数据
INSERT INTO chain_type_dict (id, chain_name, chain_code, address_prefix, native_asset, decimals, is_evm, status, remark) VALUES
(1, '波场链（TRON）', 'TRX', 'T', 'TRX', 6, FALSE, 1, '支持TRC20代币'),
(2, '以太坊（Ethereum）', 'ETH', '0x', 'ETH', 18, TRUE, 1, '支持ERC20代币'),
(3, '币安智能链（BSC）', 'BSC', '0x', 'BNB', 18, TRUE, 1, '支持BEP20代币（EVM兼容）'),
(4, '比特币（Bitcoin）', 'BTC', '1,3,bc1', 'BTC', 8, FALSE, 1, '原生链，无代币标准');

-- 重置序列
SELECT setval('chain_type_dict_id_seq', 4, true);

-- 插入代币数据
INSERT INTO token (token_id, chain_id, token_name, token_symbol, token_contract, token_decimals) VALUES
('TRX_TRX_NATIVE', 1, 'TRON（原生币）', 'TRX', NULL, 6),
('TRX_USDT_TR7NHqjeK', 1, 'Tether USD（TRC20）', 'USDT', 'TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t', 6),
('ETH_ETH_NATIVE', 2, 'Ethereum（原生币）', 'ETH', NULL, 18),
('ETH_USDT_0xdAC17F', 2, 'Tether USD（ERC20）', 'USDT', '******************************************', 6),
('BSC_BNB_NATIVE', 3, 'Binance Coin（原生币）', 'BNB', NULL, 18),
('BSC_USDT_0x55d398', 3, 'Tether USD（BEP20）', 'USDT', '******************************************', 18),
('BTC_BTC_NATIVE', 4, 'Bitcoin（原生币）', 'BTC', NULL, 8);

-- 插入示例商户
INSERT INTO merchant (id, merchant_name, callback_url, status) VALUES
(1, 'xn3469010', 'http://**************:8999/callback', 1),
(888, 'shanghu', 'http://**************:8999/callback', 1);

-- 重置序列
SELECT setval('merchant_id_seq', 888, true);

-- =====================================================
-- 创建视图（便于查询）
-- =====================================================

-- 地址资产汇总视图
CREATE OR REPLACE VIEW v_address_balance AS
SELECT
    am.id as address_main_id,
    am.merchant_id,
    am.user_id,
    am.group_name,
    ac.id as address_chain_id,
    ctd.chain_name,
    ctd.chain_code,
    ac.chain_addr,
    t.token_symbol,
    t.token_name,
    aa.balance_decimal,
    aa.last_sync_time,
    ac.sync_status,
    ac.lock_status
FROM address_main am
JOIN address_chain ac ON am.id = ac.address_main_id
JOIN chain_type_dict ctd ON ac.chain_id = ctd.id
JOIN address_asset aa ON ac.id = aa.address_chain_id
JOIN token t ON aa.token_id = t.token_id
WHERE am.deleted_at IS NULL
  AND ctd.status = 1
  AND t.token_status = 1;

-- 商户资产汇总视图
CREATE OR REPLACE VIEW v_merchant_balance AS
SELECT
    m.id as merchant_id,
    m.merchant_name,
    t.token_symbol,
    ctd.chain_code,
    SUM(aa.balance_decimal) as total_balance,
    COUNT(DISTINCT ac.id) as address_count,
    MAX(aa.last_sync_time) as last_sync_time
FROM merchant m
JOIN address_main am ON m.id = am.merchant_id
JOIN address_chain ac ON am.id = ac.address_main_id
JOIN address_asset aa ON ac.id = aa.address_chain_id
JOIN token t ON aa.token_id = t.token_id
JOIN chain_type_dict ctd ON ac.chain_id = ctd.id
WHERE am.deleted_at IS NULL
  AND m.status = 1
  AND ctd.status = 1
  AND t.token_status = 1
GROUP BY m.id, m.merchant_name, t.token_symbol, ctd.chain_code;

-- =====================================================
-- 创建有用的函数
-- =====================================================

-- 获取地址余额函数
CREATE OR REPLACE FUNCTION get_address_balance(
    p_chain_code VARCHAR(20),
    p_address VARCHAR(255),
    p_token_symbol VARCHAR(20)
) RETURNS NUMERIC(36,18) AS $$
DECLARE
    balance NUMERIC(36,18);
BEGIN
    SELECT aa.balance_decimal INTO balance
    FROM address_chain ac
    JOIN chain_type_dict ctd ON ac.chain_id = ctd.id
    JOIN address_asset aa ON ac.id = aa.address_chain_id
    JOIN token t ON aa.token_id = t.token_id
    WHERE ctd.chain_code = p_chain_code
      AND ac.chain_addr = p_address
      AND t.token_symbol = p_token_symbol;

    RETURN COALESCE(balance, 0);
END;
$$ LANGUAGE plpgsql;

-- 创建地址组函数
CREATE OR REPLACE FUNCTION create_address_group(
    p_merchant_id INTEGER,
    p_user_id VARCHAR(64) DEFAULT NULL,
    p_group_name VARCHAR(50) DEFAULT NULL
) RETURNS INTEGER AS $$
DECLARE
    new_id INTEGER;
BEGIN
    INSERT INTO address_main (merchant_id, user_id, group_name)
    VALUES (p_merchant_id, p_user_id, p_group_name)
    RETURNING id INTO new_id;

    RETURN new_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 完成
-- =====================================================
-- 数据库结构创建完成
-- 所有索引名已修复，避免冲突
-- 可以安全导入到 PostgreSQL 数据库
